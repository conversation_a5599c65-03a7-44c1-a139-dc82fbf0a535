--cpu=Cortex-M4.fp.sp
".\objects\gd32f4xx_it.o"
".\objects\gd32f470vet6_bsp.o"
".\objects\main.o"
".\objects\scheduler.o"
".\objects\systick.o"
".\objects\adc_app.o"
".\objects\led_app.o"
".\objects\btn_app.o"
".\objects\oled_app.o"
".\objects\rtc_app.o"
".\objects\tf_app.o"
".\objects\usart_app.o"
".\objects\rs485_app.o"
".\objects\ebtn.o"
".\objects\oled.o"
".\objects\gd25qxx.o"
".\objects\sdio_sdcard.o"
".\objects\diskio.o"
".\objects\ff.o"
".\objects\unicode.o"
".\objects\system_gd32f4xx.o"
".\objects\startup_gd32f450_470.o"
".\objects\gd32f4xx_adc.o"
".\objects\gd32f4xx_can.o"
".\objects\gd32f4xx_crc.o"
".\objects\gd32f4xx_ctc.o"
".\objects\gd32f4xx_dac.o"
".\objects\gd32f4xx_dbg.o"
".\objects\gd32f4xx_dci.o"
".\objects\gd32f4xx_dma.o"
".\objects\gd32f4xx_enet.o"
".\objects\gd32f4xx_exmc.o"
".\objects\gd32f4xx_exti.o"
".\objects\gd32f4xx_fmc.o"
".\objects\gd32f4xx_fwdgt.o"
".\objects\gd32f4xx_gpio.o"
".\objects\gd32f4xx_i2c.o"
".\objects\gd32f4xx_ipa.o"
".\objects\gd32f4xx_iref.o"
".\objects\gd32f4xx_misc.o"
".\objects\gd32f4xx_pmu.o"
".\objects\gd32f4xx_rcu.o"
".\objects\gd32f4xx_rtc.o"
".\objects\gd32f4xx_sdio.o"
".\objects\gd32f4xx_spi.o"
".\objects\gd32f4xx_syscfg.o"
".\objects\gd32f4xx_timer.o"
".\objects\gd32f4xx_tli.o"
".\objects\gd32f4xx_trng.o"
".\objects\gd32f4xx_usart.o"
".\objects\gd32f4xx_wwdgt.o"
--strict --scatter ".\Objects\Project.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\Listings\Project.map" -o .\Objects\Project.axf