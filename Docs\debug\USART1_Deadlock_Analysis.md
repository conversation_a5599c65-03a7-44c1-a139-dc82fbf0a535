# USART1串口发送导致板子卡死问题分析报告

## 问题描述
向串口1(USART1)发送字符串后，板子出现卡死现象。

## 根本原因分析

### 1. 关键问题：USART1中断处理函数被注释掉
在 `Core\Src\gd32f4xx_it.c` 文件中，第182-202行的 `USART1_IRQHandler` 函数被完全注释掉：

```c
// void USART1_IRQHandler(void)
// {
//     if(RESET != usart_interrupt_flag_get(USART1, USART_INT_FLAG_IDLE)){
//         /* clear IDLE flag */
//         usart_data_receive(USART1);
//         // ... 其他处理代码
//     }
// }
```

### 2. 死循环位置分析
在 `rs485_printf` 和 `rs485_send_string` 函数中存在以下死循环：

**rs485_printf函数 (第26-30行):**
```c
while(RESET == usart_flag_get(RS485_USART, USART_FLAG_TBE))
{
    ucLed[1] = 1;  // LED1会一直亮着表示卡在这里
};
```

**rs485_printf函数 (第34行):**
```c
while(RESET == usart_flag_get(RS485_USART, USART_FLAG_TC));
```

**rs485_send_string函数 (第51行和第55行):**
```c
while(RESET == usart_flag_get(RS485_USART, USART_FLAG_TBE));
while(RESET == usart_flag_get(RS485_USART, USART_FLAG_TC));
```

### 3. 问题机制
1. **RS485_USART** 宏定义为 **USART1**
2. 程序调用 `rs485_printf` 或 `rs485_send_string` 发送数据
3. 发送函数等待 `USART_FLAG_TBE`(发送缓冲区空)和 `USART_FLAG_TC`(传输完成)标志
4. 由于 **USART1中断处理函数被注释**，USART1的硬件标志位无法被正确清除
5. 导致标志位一直为RESET状态，while循环永远无法退出
6. 程序卡死在死循环中

### 4. 验证方法
观察LED1的状态：
- 如果LED1持续亮着不闪烁，说明卡在第26-30行的while循环中
- 这证实了USART1发送缓冲区空标志位无法被设置

## 解决方案

### 方案1：启用USART1中断处理函数（推荐）
取消注释 `USART1_IRQHandler` 函数：

```c
void USART1_IRQHandler(void)
{
    if(RESET != usart_interrupt_flag_get(USART1, USART_INT_FLAG_IDLE)){
        /* clear IDLE flag */
        usart_data_receive(USART1);
        
        /* number of data received */
        uint16_t rx_len = 512 - dma_transfer_number_get(DMA0, DMA_CH5);
        if(rx_len > 0) {
            memcpy(rs485_dma_buffer, rs485_rxbuffer, rx_len);
            memset(rs485_rxbuffer, 0, 512);
            rs485_rx_flag = 1;
        }
        
        /* disable DMA and reconfigure */
        dma_channel_disable(DMA0, DMA_CH5);
        dma_flag_clear(DMA0, DMA_CH5, DMA_FLAG_FTF);
        dma_transfer_number_config(DMA0, DMA_CH5, 512);
        dma_channel_enable(DMA0, DMA_CH5);
    }
}
```

### 方案2：添加超时机制
参考USART0的实现，为RS485发送函数添加超时保护：

```c
int rs485_printf(const char *format, ...)
{
    // ... 前面代码不变
    
    // 发送数据 - 添加超时保护
    for(int i = 0; i < len; i++){
        usart_data_transmit(RS485_USART, buffer[i]);
        
        uint32_t timeout_ms = uwTick;
        while(RESET == usart_flag_get(RS485_USART, USART_FLAG_TBE))
        {
            ucLed[1] = 1;
            if ((uwTick - timeout_ms) > 100) // 100ms超时
            {
                ucLed[1] = 0;
                rs485_set_rx_mode();
                return i;  // 返回已发送字节数
            }
        };
        ucLed[1] = 0;
    }
    
    // 等待发送完成 - 添加超时保护
    uint32_t timeout_ms = uwTick;
    while(RESET == usart_flag_get(RS485_USART, USART_FLAG_TC))
    {
        if ((uwTick - timeout_ms) > 100) // 100ms超时
        {
            rs485_set_rx_mode();
            return len;
        }
    }
    
    // ... 后面代码不变
}
```

## 建议
1. **立即采用方案1**：取消注释USART1中断处理函数，这是根本解决方案
2. **同时实施方案2**：添加超时机制作为额外保护
3. **测试验证**：修复后测试RS485通信功能是否正常

## 相关文件
- `Core\Src\gd32f4xx_it.c` - 中断处理函数
- `Core\Src\rs485_app.c` - RS485应用层代码
- `Core\Inc\gd32f470vet6_bsp.h` - 宏定义

## 修复优先级
**高优先级** - 此问题会导致系统完全卡死，需要立即修复。